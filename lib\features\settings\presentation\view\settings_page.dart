import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

class SettingsPage extends GetView<PrintersIPAddressController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: AppTextStyle.primary18800,
        ),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Printer Settings',
              style: AppTextStyle.primary18800.copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildAddPrinterSection(),
            const SizedBox(height: 24),
            Text(
              'Configured Printers',
              style: AppTextStyle.primary16800.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: _buildPrintersList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddPrinterSection() {
    final TextEditingController ipController = TextEditingController();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add New Printer',
              style: AppTextStyle.primary16800.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: ipController,
                    decoration: const InputDecoration(
                      labelText: 'Printer IP Address',
                      hintText: 'e.g., *************',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    final ip = ipController.text.trim();
                    if (controller.isValidIP(ip)) {
                      controller.addPrinterIP(ip);
                      ipController.clear();
                    } else {
                      Get.snackbar(
                        'Error',
                        'Please enter a valid IP address',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Add'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrintersList() {
    return Obx(() {
      if (controller.printerIPs.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.print_disabled,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No printers configured',
                style: AppTextStyle.primary16800.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add a printer IP address above to get started',
                style: AppTextStyle.primary12700.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: controller.printerIPs.length,
        itemBuilder: (context, index) {
          final ip = controller.printerIPs[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: const Icon(
                Icons.print,
                color: AppColors.primaryColor,
              ),
              title: Text(
                ip,
                style: AppTextStyle.primary16800,
              ),
              subtitle: Text(
                'Network Printer',
                style: AppTextStyle.primary12700.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              trailing: IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                ),
                onPressed: () {
                  _showDeleteConfirmation(ip);
                },
              ),
            ),
          );
        },
      );
    });
  }

  void _showDeleteConfirmation(String ip) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Printer'),
        content: Text('Are you sure you want to remove printer $ip?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              controller.removePrinterIP(ip);
              Get.back();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
