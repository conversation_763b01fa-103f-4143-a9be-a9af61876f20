import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class PrintersIPAddressController extends GetxController {
  final GetStorage _storage = GetStorage();
  
  // Observable list of printer IP addresses
  final RxList<String> printerIPs = <String>[].obs;
  
  // Key for storing printer IPs in local storage
  static const String _printersKey = 'printer_ips';
  
  @override
  void onInit() {
    super.onInit();
    _loadPrinterIPs();
  }
  
  /// Load printer IPs from local storage
  void _loadPrinterIPs() {
    final List<dynamic>? storedIPs = _storage.read(_printersKey);
    if (storedIPs != null) {
      printerIPs.assignAll(storedIPs.cast<String>());
    }
  }
  
  /// Save printer IPs to local storage
  void _savePrinterIPs() {
    _storage.write(_printersKey, printerIPs.toList());
  }
  
  /// Add a new printer IP address
  void addPrinterIP(String ip) {
    if (ip.isNotEmpty && !printerIPs.contains(ip)) {
      printerIPs.add(ip);
      _savePrinterIPs();
      Get.snackbar(
        'Success',
        'Printer IP added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else if (printerIPs.contains(ip)) {
      Get.snackbar(
        'Warning',
        'This IP address already exists',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  /// Remove a printer IP address
  void removePrinterIP(String ip) {
    printerIPs.remove(ip);
    _savePrinterIPs();
    Get.snackbar(
      'Success',
      'Printer IP removed successfully',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  /// Clear all printer IPs
  void clearAllPrinterIPs() {
    printerIPs.clear();
    _savePrinterIPs();
    Get.snackbar(
      'Success',
      'All printer IPs cleared',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  /// Validate IP address format
  bool isValidIP(String ip) {
    final RegExp ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    );
    return ipRegex.hasMatch(ip);
  }
  
  /// Get all printer IPs
  List<String> getAllPrinterIPs() {
    return printerIPs.toList();
  }
  
  /// Check if any printers are configured
  bool get hasPrinters => printerIPs.isNotEmpty;
}
