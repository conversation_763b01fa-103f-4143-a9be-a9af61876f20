import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class PrintersIPAddressController extends GetxController {
  final GetStorage _storage = GetStorage();

  // Observable list of printer IP addresses
  final RxList<String> printerIPs = <String>[].obs;

  // Key for storing printer IPs in local storage
  static const String _printersKey = 'printer_ips';

  @override
  void onInit() {
    super.onInit();
    _loadPrinterIPs();
  }

  /// Load printer IPs from local storage
  void _loadPrinterIPs() {
    final List<dynamic>? storedIPs = _storage.read(_printersKey);
    if (storedIPs != null) {
      printerIPs.assignAll(storedIPs.cast<String>());
    }
  }

  /// Save printer IPs to local storage
  void _savePrinterIPs() {
    _storage.write(_printersKey, printerIPs.toList());
  }

  /// Add a new printer IP address
  void addPrinterIP(String ip) {
    if (ip.isNotEmpty && !printerIPs.contains(ip)) {
      printerIPs.add(ip);
      _savePrinterIPs();
      Get.snackbar(
        'Success',
        'Printer IP added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else if (printerIPs.contains(ip)) {
      Get.snackbar(
        'Warning',
        'This IP address already exists',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Remove a printer IP address
  void removePrinterIP(String ip) {
    printerIPs.remove(ip);
    _savePrinterIPs();
    Get.snackbar(
      'Success',
      'Printer IP removed successfully',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Clear all printer IPs
  void clearAllPrinterIPs() {
    printerIPs.clear();
    _savePrinterIPs();
    Get.snackbar(
      'Success',
      'All printer IPs cleared',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Validate IP address format
  bool isValidIP(String ip) {
    final RegExp ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    );
    return ipRegex.hasMatch(ip);
  }

  /// Get all printer IPs
  List<String> getAllPrinterIPs() {
    return printerIPs.toList();
  }

  /// Check if any printers are configured
  bool get hasPrinters => printerIPs.isNotEmpty;

  /// Test connection to a specific printer IP
  Future<bool> testPrinterConnection(String ip) async {
    try {
      Get.log('Testing connection to XPrinter K200L at IP: $ip');

      // Try multiple ports commonly used by XPrinter models
      final List<int> portsToTry = [9100, 515, 631, 8080, 80];

      for (int port in portsToTry) {
        try {
          Get.log('Testing connection to $ip:$port');

          final printer = PrinterNetworkManager(ip, port: port);

          // Create a simple test print (just initialize, don't actually print)
          final profile = await CapabilityProfile.load();
          final generator = Generator(PaperSize.mm80, profile);
          final List<int> testBytes = [];
          testBytes.addAll(generator.text('Test Connection'));
          testBytes.addAll(generator.cut());

          // Try to connect with timeout
          final result = await printer.printTicket(testBytes).timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              Get.log('Timeout testing $ip:$port');
              return PosPrintResult.timeout;
            },
          );

          if (result == PosPrintResult.success) {
            Get.log('Successfully connected to $ip:$port');
            Get.snackbar(
              'Success',
              'Printer connection test successful on port $port',
              snackPosition: SnackPosition.BOTTOM,
            );
            return true;
          }
        } catch (e) {
          Get.log('Connection test failed for $ip:$port - $e');
          continue;
        }
      }

      Get.snackbar(
        'Error',
        'Failed to connect to printer $ip on all ports',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } catch (e) {
      Get.log('Printer test error: $e');
      Get.snackbar(
        'Error',
        'Printer test failed: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
}
