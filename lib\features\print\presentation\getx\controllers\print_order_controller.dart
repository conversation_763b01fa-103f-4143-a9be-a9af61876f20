import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

class PrintOrderController extends GetxController {
  /// Helper method to handle Arabic text encoding
  String _sanitizeText(String text) {
    // If text contains Arabic characters, use transliteration
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(text)) {
      return _transliterateArabic(text);
    }
    // For non-Arabic text, just return as is
    return text;
  }

  /// Alternative method to convert Arabic text to Latin transliteration for problematic cases
  String _transliterateArabic(String text) {
    final arabicToLatin = {
      'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h',
      'خ': 'kh', 'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's',
      'ش': 'sh', 'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a',
      'غ': 'gh', 'ف': 'f', 'ق': 'q', 'ك': 'k', 'ل': 'l', 'م': 'm',
      'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y', 'ة': 'h', 'ى': 'a',
      'أ': 'a', 'إ': 'i', 'آ': 'aa', 'ؤ': 'w', 'ئ': 'y',
      ' ': ' ', // Keep spaces
    };

    String result = '';
    for (int i = 0; i < text.length; i++) {
      String char = text[i];
      result += arabicToLatin[char] ?? char;
    }
    return result;
  }

  /// Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  final OrderController orderController = Get.find<OrderController>();
  Future<void> printInvoice(PrintController controller) async {
    // Get cached invoice data
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();

    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    final invoiceNumber = controller.orderNumber.toString();
    final double subtotal = double.parse(controller.totalPrice.toString());
    final double vatAmount = subtotal * (double.parse(vat) / 100);
    final double totalIncludingVat = subtotal + vatAmount;

    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Get order details controller
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(
          printerIP,
          controller,
          orderDetailsController,
          invoiceData,
          invoiceNumber,
          subtotal,
          vatAmount,
          totalIncludingVat,
        ));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Print to a single network printer
  Future<void> _printToSinglePrinter(
    String printerIP,
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    Get.log('Attempting to print to XPrinter K200L at IP: $printerIP');

    try {
      // Create network printer instance with specific settings for XPrinter K200L
      final profile = await CapabilityProfile.load();

      // Try multiple ports commonly used by XPrinter models
      final List<int> portsToTry = [9100, 515, 631, 8080, 80];
      PosPrintResult? lastResult;
      Exception? lastException;

      for (int port in portsToTry) {
        try {
          Get.log('Trying to connect to $printerIP:$port');

          final printer = PrinterNetworkManager(printerIP, port: port);

          // Generate ESC/POS commands
          final List<int> bytes = await _generateEscPosCommands(
            controller,
            orderDetailsController,
            invoiceData,
            invoiceNumber,
            subtotal,
            vatAmount,
            totalIncludingVat,
            profile,
          );

          // Try to print with timeout
          final result = await printer.printTicket(bytes).timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              Get.log('Timeout connecting to $printerIP:$port');
              return PosPrintResult.timeout;
            },
          );

          if (result == PosPrintResult.success) {
            Get.log('Successfully printed to $printerIP:$port');
            await Future.delayed(const Duration(milliseconds: 500));
            return; // Success, exit the method
          } else {
            Get.log('Print failed on $printerIP:$port with result: $result');
            lastResult = result;
          }
        } catch (e) {
          Get.log('Exception on $printerIP:$port - $e');
          lastException = Exception(e.toString());
          continue; // Try next port
        }
      }

      // If we get here, all ports failed
      throw lastException ??
          Exception(
              'Failed to print to $printerIP on all ports. Last result: $lastResult');
    } catch (e) {
      Get.log('Print error for $printerIP: $e');
      throw Exception('Error printing to $printerIP: $e');
    }
  }

  /// Generate ESC/POS commands for the invoice
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Create generator for ESC/POS commands optimized for XPrinter K200L
    final generator = Generator(PaperSize.mm80, profile);
    List<int> bytes = [];

    // Initialize printer - XPrinter K200L specific commands
    bytes += generator.reset();

    // Header - Use transliteration for Arabic text to avoid encoding issues
    final sanitizedRestaurantName = _sanitizeText(restaurntName);
    Get.log('Original restaurant name: $restaurntName');
    Get.log('Sanitized restaurant name: $sanitizedRestaurantName');

    bytes += generator.text(sanitizedRestaurantName,
        styles: const PosStyles(align: PosAlign.center, bold: true));
    bytes += generator.text('Simplified Tax Invoice',
        styles: const PosStyles(align: PosAlign.center, bold: true));
    bytes += generator.text('Fatorah Daribiah Mubassatah',
        styles: const PosStyles(align: PosAlign.center, bold: true));
    bytes += generator.emptyLines(1);

    // Order number
    bytes += generator.text('Order #${invoiceNumber.trim()}',
        styles: const PosStyles(
            align: PosAlign.center, bold: true, height: PosTextSize.size2));
    bytes += generator.emptyLines(1);

    // Company information - Use English/Latin text to avoid encoding issues
    bytes += generator.row([
      PosColumn(
          text: 'VAT', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: vatNumber,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Tax Number',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'C.R', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: commerce,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Commercial Reg',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'POS', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: _sanitizeText(mainAddress),
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Point of Sale',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'Cashier',
          width: 3,
          styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: _sanitizeText(cashierName),
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Cashier',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'Date',
          width: 3,
          styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now()),
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Date',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.hr();

    // Items header
    bytes += generator.row([
      PosColumn(
          text: 'Qty',
          width: 2,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Item',
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Price',
          width: 4,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
    ]);
    bytes += generator.hr();

    // Print items
    final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
    for (final item in items) {
      bytes += generator.row([
        PosColumn(
            text: item.qty ?? '0',
            width: 2,
            styles: const PosStyles(align: PosAlign.center)),
        PosColumn(
            text: _sanitizeText(item.productName ?? ''),
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
        PosColumn(
            text: '${item.totalPrice ?? '0.00'} SR',
            width: 4,
            styles: const PosStyles(align: PosAlign.right)),
      ]);
    }

    bytes += generator.hr();

    // Totals
    bytes += generator.row([
      PosColumn(
          text: 'Subtotal',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${subtotal.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'VAT ($vat%)',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${vatAmount.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      PosColumn(
          text: 'Total including VAT',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${totalIncludingVat.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    bytes += generator.hr();

    // Payment methods
    final paymentTransactions =
        orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
            [];
    for (final transaction in paymentTransactions) {
      final paymentMethodName =
          '${_sanitizeText(transaction.paymentMethod?.description?[1].name ?? '')} ${_sanitizeText(transaction.paymentMethod?.description?[0].name ?? '')}';
      bytes += generator.row([
        PosColumn(
            text: paymentMethodName,
            width: 8,
            styles: const PosStyles(align: PosAlign.left, bold: true)),
        PosColumn(
            text: '${transaction.amount ?? ''} SR',
            width: 4,
            styles: const PosStyles(align: PosAlign.right, bold: true)),
      ]);
    }

    bytes += generator.emptyLines(2);

    // Footer
    bytes += generator.text('Powered by $websiteUrl',
        styles: const PosStyles(align: PosAlign.center));
    bytes += generator.emptyLines(3);

    // XPrinter K200L specific ending commands
    bytes += generator.feed(2);
    bytes += generator.cut();
    bytes += generator.drawer(); // Open cash drawer if connected

    Get.log('Generated ${bytes.length} bytes for XPrinter K200L');
    return bytes;
  }
}
