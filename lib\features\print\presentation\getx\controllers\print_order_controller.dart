import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  /// Helper method to handle Arabic text encoding

  final OrderController orderController = Get.find<OrderController>();

  /// Debug method to test invoice generation without printing
  Future<void> testInvoiceGeneration(PrintController controller) async {
    try {
      Get.log('Testing invoice generation...');

      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = double.parse(controller.totalPrice.toString());
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      final profile = await CapabilityProfile.load();

      final bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      Get.log('Invoice generation successful! Generated ${bytes.length} bytes');
      successSnackBar(
        'Invoice generation test successful! ${bytes.length} bytes generated',
      );
    } catch (e) {
      Get.log('Invoice generation failed: $e');
      failedSnaskBar(
        'Invoice generation test failed: $e',
      );
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // Check if running on web
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }

    // Mobile/Desktop printing logic
    await _printInvoiceMobile(controller);
  }

  /// Web-specific printing method - uses image capture of the invoice widget
  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');

      // Capture the invoice widget as an image and convert to PDF
      await _printInvoiceAsImagePdf(controller);

      // Show success message
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  /// Print invoice by capturing the widget as an image and converting to PDF
  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image');

      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image
      final imageBytes = await _captureWidgetAsImage(invoiceWidget);

      // Convert image to PDF
      final pdfBytes = await _createPdfFromImage(imageBytes);

      // Open print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image
  Future<Uint8List> _captureWidgetAsImage(Widget widget) async {
    try {
      // Create a RepaintBoundary to capture the widget
      final GlobalKey repaintBoundaryKey = GlobalKey();

      // Wrap the widget in a RepaintBoundary
      final repaintBoundary = RepaintBoundary(
        key: repaintBoundaryKey,
        child: Material(
          child: Container(
            color: Colors.white,
            child: widget,
          ),
        ),
      );

      // We need to render this widget off-screen
      // Create a temporary overlay to render the widget
      final overlay = Overlay.of(Get.context!);
      late OverlayEntry overlayEntry;

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: -10000, // Position off-screen
          top: -10000,
          child: repaintBoundary,
        ),
      );

      // Insert the overlay
      overlay.insert(overlayEntry);

      // Wait for the widget to be rendered
      await Future.delayed(const Duration(milliseconds: 100));

      // Capture the image
      final RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      // Remove the overlay
      overlayEntry.remove();

      if (byteData == null) {
        throw Exception('Failed to capture widget as image');
      }

      return byteData.buffer.asUint8List();
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  /// Create a PDF from an image
  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();

    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Convert captured image to ESC/POS commands for thermal printers
  Future<List<int>> _convertImageToEscPos(Uint8List imageBytes) async {
    try {
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile);
      List<int> bytes = [];

      // Initialize printer
      bytes += generator.reset();

      // Print the image
      final image = img.decodeImage(imageBytes);
      if (image != null) {
        // Resize image to fit thermal printer width (around 500-550 pixels for 80mm)
        final resizedImage = img.copyResize(image, width: 500);
        bytes += generator.imageRaster(resizedImage);
      }

      // Add some spacing and cut
      bytes += generator.feed(3);
      bytes += generator.cut();

      return bytes;
    } catch (e) {
      Get.log('Error converting image to ESC/POS: $e');
      rethrow;
    }
  }

  /// Print image-based ESC/POS data to a single printer
  Future<void> _printImageToSinglePrinter(
      String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing image to $printerIP');

      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);

      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed image to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing image to $printerIP: $e');
      rethrow;
    }
  }

  /// Mobile/Desktop printing method - now uses image capture
  Future<void> _printInvoiceMobile(PrintController controller) async {
    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting image-based print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Capture the invoice widget as an image
      final invoiceWidget = Invoice(controller: controller);
      final imageBytes = await _captureWidgetAsImage(invoiceWidget);

      // Convert image to ESC/POS commands for thermal printers
      final escPosBytes = await _convertImageToEscPos(imageBytes);

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printImageToSinglePrinter(printerIP, escPosBytes));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      p
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Generate ESC/POS commands for the invoice
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Create generator for ESC/POS commands optimized for XPrinter K200L
    final generator = Generator(PaperSize.mm80, profile);
    List<int> bytes = [];

    try {
      // Initialize printer - XPrinter K200L specific commands
      bytes += generator.reset();

      // Header - Use transliteration for Arabic text to avoid encoding issues
      final sanitizedRestaurantName = restaurntName;
      Get.log('Original restaurant name: $restaurntName');
      Get.log('Sanitized restaurant name: $sanitizedRestaurantName');

      // Simplified header for better compatibility
      bytes += generator.text(sanitizedRestaurantName,
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.text('Tax Invoice',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    } catch (e) {
      Get.log('Error in header generation: $e');
      // Fallback to basic text
      bytes += generator.text('INVOICE',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    }

    try {
      // Order number - simplified
      bytes += generator.text('Order #${invoiceNumber.trim()}',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    } catch (e) {
      Get.log('Error in order number generation: $e');
      bytes += generator.text('Order: $invoiceNumber');
      bytes += generator.emptyLines(1);
    }

    try {
      // Company information - Simplified format for better compatibility
      bytes += generator.text('VAT: $vatNumber',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('C.R: $commerce',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('Address: $mainAddress',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('Cashier: $cashierName',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text(
          'Date: ${DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now())}',
          styles: const PosStyles(align: PosAlign.center));
    } catch (e) {
      Get.log('Error in company info generation: $e');
      // Fallback to basic info
      bytes += generator.text('VAT: $vatNumber');
      bytes += generator
          .text('Date: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}');
    }

    bytes += generator.hr();

    try {
      // Items header - simplified
      bytes += generator.text('ITEMS',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.hr();

      // Print items - simplified format
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        final itemName = item.productName ?? 'Item';
        final qty = item.qty ?? '0';
        final price = item.totalPrice ?? '0.00';

        bytes += generator.text('$qty x $itemName');
        bytes += generator.text('Price: $price SR',
            styles: const PosStyles(align: PosAlign.right));
        bytes += generator.emptyLines(1);
      }
    } catch (e) {
      Get.log('Error in items generation: $e');
      // Fallback to basic items list
      bytes += generator.text('ITEMS:');
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        bytes += generator
            .text('${item.qty ?? '0'} x ${item.productName ?? 'Item'}');
      }
    }

    bytes += generator.hr();

    try {
      // Totals - simplified format
      bytes += generator.text('Subtotal: ${subtotal.toStringAsFixed(2)} SR',
          styles: const PosStyles(align: PosAlign.right, bold: true));
      bytes += generator.text('VAT ($vat%): ${vatAmount.toStringAsFixed(2)} SR',
          styles: const PosStyles(align: PosAlign.right, bold: true));
      bytes += generator.text(
          'TOTAL: ${totalIncludingVat.toStringAsFixed(2)} SR',
          styles: const PosStyles(
              align: PosAlign.center, bold: true, height: PosTextSize.size2));
    } catch (e) {
      Get.log('Error in totals generation: $e');
      // Fallback to basic totals
      bytes += generator.text('Subtotal: ${subtotal.toStringAsFixed(2)} SR');
      bytes += generator.text('VAT: ${vatAmount.toStringAsFixed(2)} SR');
      bytes += generator.text(
          'TOTAL: ${totalIncludingVat.toStringAsFixed(2)} SR',
          styles: const PosStyles(bold: true));
    }

    bytes += generator.hr();

    try {
      // Payment methods - simplified
      final paymentTransactions =
          orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
              [];

      if (paymentTransactions.isNotEmpty) {
        bytes += generator.text('PAYMENT METHODS:',
            styles: const PosStyles(bold: true));

        for (final transaction in paymentTransactions) {
          final paymentMethodName =
              '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}'
                  .trim();
          final amount = transaction.amount ?? '';

          bytes += generator.text('$paymentMethodName: $amount SR');
        }
      }
    } catch (e) {
      Get.log('Error in payment methods generation: $e');
      // Skip payment methods if there's an error
    }

    bytes += generator.emptyLines(2);

    try {
      // Footer - simplified
      if (websiteUrl.isNotEmpty) {
        bytes += generator.text('Powered by $websiteUrl',
            styles: const PosStyles(align: PosAlign.center));
      }
      bytes += generator.emptyLines(2);

      // XPrinter K200L specific ending commands
      bytes += generator.feed(2);
      bytes += generator.cut();

      Get.log('Generated ${bytes.length} bytes for XPrinter K200L');
      return bytes;
    } catch (e) {
      Get.log('Error in footer generation: $e');
      // Minimal ending
      bytes += generator.emptyLines(3);
      bytes += generator.cut();
      return bytes;
    }
  }
}
