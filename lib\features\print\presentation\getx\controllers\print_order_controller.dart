import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:qr_flutter/qr_flutter.dart';

class PrintOrderController extends GetxController {
  /// Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  final OrderController orderController = Get.find<OrderController>();
  Future<void> printInvoice(PrintController controller) async {
    // Get cached invoice data
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();

    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    final invoiceNumber = controller.orderNumber.toString();
    final double subtotal = double.parse(controller.totalPrice.toString());
    final double vatAmount = subtotal * (double.parse(vat) / 100);
    final double totalIncludingVat = subtotal + vatAmount;

    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error',
        'No printers configured. Please add printer IPs in settings.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    try {
      // Get order details controller
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(
          printerIP,
          controller,
          orderDetailsController,
          invoiceData,
          invoiceNumber,
          subtotal,
          vatAmount,
          totalIncludingVat,
        ));
      }

      // Wait for all print tasks to complete
      await Future.wait(printTasks);

      Get.snackbar(
        'Success',
        'Invoice printed to ${printerIPs.length} printer(s) successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Print to a single network printer
  Future<void> _printToSinglePrinter(
    String printerIP,
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    try {
      // Create network printer instance
      final printer =
          NetworkPrinter(PaperSize.mm80, await CapabilityProfile.load());

      // Connect to printer
      final PosPrintResult connectResult =
          await printer.connect(printerIP, port: 9100);

      if (connectResult != PosPrintResult.success) {
        throw Exception('Failed to connect to printer $printerIP');
      }

      // Generate ESC/POS commands
      await _generateEscPosCommands(
        printer,
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
      );

      // Disconnect from printer
      printer.disconnect();
    } catch (e) {
      throw Exception('Error printing to $printerIP: $e');
    }
  }

  /// Generate ESC/POS commands for the invoice
  Future<void> _generateEscPosCommands(
    NetworkPrinter printer,
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Header
    printer.text(restaurntName,
        styles: const PosStyles(align: PosAlign.center, bold: true));
    printer.text('فاتورة ضريبية مبسطة',
        styles: const PosStyles(align: PosAlign.center, bold: true));
    printer.text('Simplified Tax Invoice',
        styles: const PosStyles(align: PosAlign.center, bold: true));
    printer.emptyLines(1);

    // Order number
    printer.text('Order #${invoiceNumber.trim()}',
        styles: const PosStyles(
            align: PosAlign.center, bold: true, height: PosTextSize.size2));
    printer.emptyLines(1);

    // Company information
    printer.row([
      PosColumn(
          text: 'VAT', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: vatNumber,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'الرقم الضريبي',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: 'C.R', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: commerce,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'السجل التجاري',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: 'POS', width: 3, styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: mainAddress,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'نقطة البيع',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: 'Cashier',
          width: 3,
          styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: cashierName,
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'الكاشير',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: 'Date',
          width: 3,
          styles: const PosStyles(align: PosAlign.left)),
      PosColumn(
          text: DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now()),
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'التاريخ',
          width: 3,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.hr();

    // Items header
    printer.row([
      PosColumn(
          text: 'Qty\nالكمية',
          width: 2,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Item\nالصنف',
          width: 6,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
      PosColumn(
          text: 'Price\nالسعر',
          width: 4,
          styles: const PosStyles(align: PosAlign.center, bold: true)),
    ]);
    printer.hr();

    // Print items
    final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
    for (final item in items) {
      printer.row([
        PosColumn(
            text: item.qty ?? '0',
            width: 2,
            styles: const PosStyles(align: PosAlign.center)),
        PosColumn(
            text: item.productName ?? '',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
        PosColumn(
            text: '${item.totalPrice ?? '0.00'} SR',
            width: 4,
            styles: const PosStyles(align: PosAlign.right)),
      ]);
    }

    printer.hr();

    // Totals
    printer.row([
      PosColumn(
          text: 'المجموع بدون الضريبة - Subtotal',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${subtotal.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: '%($vat) الضريبة - VAT',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${vatAmount.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.row([
      PosColumn(
          text: 'الإجمالي شامل الضريبة - Total including VAT',
          width: 8,
          styles: const PosStyles(align: PosAlign.left, bold: true)),
      PosColumn(
          text: '${totalIncludingVat.toStringAsFixed(2)} SR',
          width: 4,
          styles: const PosStyles(align: PosAlign.right, bold: true)),
    ]);

    printer.hr();

    // Payment methods
    final paymentTransactions =
        orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
            [];
    for (final transaction in paymentTransactions) {
      final paymentMethodName =
          '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}';
      printer.row([
        PosColumn(
            text: paymentMethodName,
            width: 8,
            styles: const PosStyles(align: PosAlign.left, bold: true)),
        PosColumn(
            text: '${transaction.amount ?? ''} SR',
            width: 4,
            styles: const PosStyles(align: PosAlign.right, bold: true)),
      ]);
    }

    printer.emptyLines(2);

    // Footer
    printer.text('Powered by $websiteUrl',
        styles: const PosStyles(align: PosAlign.center));
    printer.emptyLines(3);
    printer.cut();
  }
}
