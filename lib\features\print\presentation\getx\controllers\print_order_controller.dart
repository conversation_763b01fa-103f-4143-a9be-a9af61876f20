import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';

class PrintOrderController extends GetxController {
  /// Helper method to handle Arabic text encoding
  String _sanitizeText(String text) {
    // If text contains Arabic characters, use transliteration
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(text)) {
      return _transliterateArabic(text);
    }
    // For non-Arabic text, just return as is
    return text;
  }

  /// Alternative method to convert Arabic text to Latin transliteration for problematic cases
  String _transliterateArabic(String text) {
    final arabicToLatin = {
      'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h',
      'خ': 'kh', 'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's',
      'ش': 'sh', 'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a',
      'غ': 'gh', 'ف': 'f', 'ق': 'q', 'ك': 'k', 'ل': 'l', 'م': 'm',
      'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y', 'ة': 'h', 'ى': 'a',
      'أ': 'a', 'إ': 'i', 'آ': 'aa', 'ؤ': 'w', 'ئ': 'y',
      ' ': ' ', // Keep spaces
    };

    String result = '';
    for (int i = 0; i < text.length; i++) {
      String char = text[i];
      result += arabicToLatin[char] ?? char;
    }
    return result;
  }

  /// Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  final OrderController orderController = Get.find<OrderController>();

  /// Debug method to test invoice generation without printing
  Future<void> testInvoiceGeneration(PrintController controller) async {
    try {
      Get.log('Testing invoice generation...');

      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = double.parse(controller.totalPrice.toString());
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      final profile = await CapabilityProfile.load();

      final bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      Get.log('Invoice generation successful! Generated ${bytes.length} bytes');
      Get.snackbar(
        'Success',
        'Invoice generation test successful! ${bytes.length} bytes generated',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
    } catch (e) {
      Get.log('Invoice generation failed: $e');
      Get.snackbar(
        'Error',
        'Invoice generation test failed: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // Check if running on web
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }

    // Mobile/Desktop printing logic
    await _printInvoiceMobile(controller);
  }

  /// Web-specific printing method with direct printer support
  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform');

      // Get cached invoice data
      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = double.parse(controller.totalPrice.toString());
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      // Get printer IPs
      final printersController = Get.find<PrintersIPAddressController>();
      final printerIPs = printersController.getAllPrinterIPs();

      if (printerIPs.isEmpty) {
        // Fallback to PDF if no printers configured
        await _printInvoiceWebPdf(controller, orderDetailsController,
            invoiceData, invoiceNumber, subtotal, vatAmount, totalIncludingVat);
        return;
      }

      // Try direct web printing to network printers
      bool anySuccess = false;
      for (String printerIP in printerIPs) {
        try {
          final success = await _printToWebPrinter(
            printerIP,
            controller,
            orderDetailsController,
            invoiceData,
            invoiceNumber,
            subtotal,
            vatAmount,
            totalIncludingVat,
          );
          if (success) anySuccess = true;
        } catch (e) {
          Get.log('Failed to print to web printer $printerIP: $e');
        }
      }

      if (anySuccess) {
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to network printer(s)',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } else {
        // Fallback to PDF if direct printing fails
        Get.log('Direct web printing failed, falling back to PDF');
        await _printInvoiceWebPdf(controller, orderDetailsController,
            invoiceData, invoiceNumber, subtotal, vatAmount, totalIncludingVat);
      }
    } catch (e) {
      Get.log('Web print error: $e');
      Get.snackbar(
        'Error'.tr,
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Mobile/Desktop printing method
  Future<void> _printInvoiceMobile(PrintController controller) async {
    // Get cached invoice data
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();

    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    final invoiceNumber = controller.orderNumber.toString();
    final double subtotal = double.parse(controller.totalPrice.toString());
    final double vatAmount = subtotal * (double.parse(vat) / 100);
    final double totalIncludingVat = subtotal + vatAmount;

    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Get order details controller
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(
          printerIP,
          controller,
          orderDetailsController,
          invoiceData,
          invoiceNumber,
          subtotal,
          vatAmount,
          totalIncludingVat,
        ));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Print to a single network printer
  Future<void> _printToSinglePrinter(
    String printerIP,
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    Get.log('Attempting to print to XPrinter K200L at IP: $printerIP');

    // First, do a simple ping test to check if the IP is reachable
    try {
      final result = await InternetAddress.lookup(printerIP).timeout(
        const Duration(seconds: 3),
        onTimeout: () => throw Exception('IP lookup timeout'),
      );
      if (result.isEmpty) {
        throw Exception('IP address not reachable');
      }
      Get.log('IP $printerIP is reachable');
    } catch (e) {
      Get.log('IP connectivity check failed: $e');
      throw Exception(
          'Cannot reach printer at $printerIP. Check network connection.');
    }

    try {
      // Create network printer instance with specific settings for XPrinter K200L
      final profile = await CapabilityProfile.load();

      // Try multiple ports commonly used by XPrinter models
      final List<int> portsToTry = [9100, 515, 631, 8080, 80];
      PosPrintResult? lastResult;
      Exception? lastException;

      // Generate ESC/POS commands once (outside the port loop)
      final List<int> bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      Get.log('Generated invoice with ${bytes.length} bytes');

      for (int port in portsToTry) {
        try {
          Get.log('Trying to print invoice to $printerIP:$port');

          final printer = PrinterNetworkManager(printerIP, port: port);

          // Try to print with timeout
          final result = await printer.printTicket(bytes).timeout(
            const Duration(seconds: 15), // Longer timeout for invoice printing
            onTimeout: () {
              Get.log('Timeout printing invoice to $printerIP:$port');
              return PosPrintResult.timeout;
            },
          );

          if (result == PosPrintResult.success) {
            Get.log('Successfully printed invoice to $printerIP:$port');
            await Future.delayed(
                const Duration(milliseconds: 1000)); // Longer delay for invoice
            return; // Success, exit the method
          } else {
            Get.log(
                'Invoice print failed on $printerIP:$port with result: $result');
            lastResult = result;
          }
        } catch (e) {
          Get.log('Exception printing invoice to $printerIP:$port - $e');
          lastException = Exception(e.toString());
          continue; // Try next port
        }
      }

      // If we get here, all ports failed
      throw lastException ??
          Exception(
              'Failed to print to $printerIP on all ports. Last result: $lastResult');
    } catch (e) {
      Get.log('Print error for $printerIP: $e');
      throw Exception('Error printing to $printerIP: $e');
    }
  }

  /// Generate ESC/POS commands for the invoice
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Create generator for ESC/POS commands optimized for XPrinter K200L
    final generator = Generator(PaperSize.mm80, profile);
    List<int> bytes = [];

    try {
      // Initialize printer - XPrinter K200L specific commands
      bytes += generator.reset();

      // Header - Use transliteration for Arabic text to avoid encoding issues
      final sanitizedRestaurantName = _sanitizeText(restaurntName);
      Get.log('Original restaurant name: $restaurntName');
      Get.log('Sanitized restaurant name: $sanitizedRestaurantName');

      // Simplified header for better compatibility
      bytes += generator.text(sanitizedRestaurantName,
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.text('Tax Invoice',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    } catch (e) {
      Get.log('Error in header generation: $e');
      // Fallback to basic text
      bytes += generator.text('INVOICE',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    }

    try {
      // Order number - simplified
      bytes += generator.text('Order #${invoiceNumber.trim()}',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.emptyLines(1);
    } catch (e) {
      Get.log('Error in order number generation: $e');
      bytes += generator.text('Order: $invoiceNumber');
      bytes += generator.emptyLines(1);
    }

    try {
      // Company information - Simplified format for better compatibility
      bytes += generator.text('VAT: $vatNumber',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('C.R: $commerce',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('Address: ${_sanitizeText(mainAddress)}',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text('Cashier: ${_sanitizeText(cashierName)}',
          styles: const PosStyles(align: PosAlign.center));
      bytes += generator.text(
          'Date: ${DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now())}',
          styles: const PosStyles(align: PosAlign.center));
    } catch (e) {
      Get.log('Error in company info generation: $e');
      // Fallback to basic info
      bytes += generator.text('VAT: $vatNumber');
      bytes += generator
          .text('Date: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}');
    }

    bytes += generator.hr();

    try {
      // Items header - simplified
      bytes += generator.text('ITEMS',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.hr();

      // Print items - simplified format
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        final itemName = _sanitizeText(item.productName ?? 'Item');
        final qty = item.qty ?? '0';
        final price = item.totalPrice ?? '0.00';

        bytes += generator.text('$qty x $itemName');
        bytes += generator.text('Price: $price SR',
            styles: const PosStyles(align: PosAlign.right));
        bytes += generator.emptyLines(1);
      }
    } catch (e) {
      Get.log('Error in items generation: $e');
      // Fallback to basic items list
      bytes += generator.text('ITEMS:');
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        bytes += generator.text(
            '${item.qty ?? '0'} x ${_sanitizeText(item.productName ?? 'Item')}');
      }
    }

    bytes += generator.hr();

    try {
      // Totals - simplified format
      bytes += generator.text('Subtotal: ${subtotal.toStringAsFixed(2)} SR',
          styles: const PosStyles(align: PosAlign.right, bold: true));
      bytes += generator.text('VAT ($vat%): ${vatAmount.toStringAsFixed(2)} SR',
          styles: const PosStyles(align: PosAlign.right, bold: true));
      bytes += generator.text(
          'TOTAL: ${totalIncludingVat.toStringAsFixed(2)} SR',
          styles: const PosStyles(
              align: PosAlign.center, bold: true, height: PosTextSize.size2));
    } catch (e) {
      Get.log('Error in totals generation: $e');
      // Fallback to basic totals
      bytes += generator.text('Subtotal: ${subtotal.toStringAsFixed(2)} SR');
      bytes += generator.text('VAT: ${vatAmount.toStringAsFixed(2)} SR');
      bytes += generator.text(
          'TOTAL: ${totalIncludingVat.toStringAsFixed(2)} SR',
          styles: const PosStyles(bold: true));
    }

    bytes += generator.hr();

    try {
      // Payment methods - simplified
      final paymentTransactions =
          orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
              [];

      if (paymentTransactions.isNotEmpty) {
        bytes += generator.text('PAYMENT METHODS:',
            styles: const PosStyles(bold: true));

        for (final transaction in paymentTransactions) {
          final paymentMethodName = _sanitizeText(
              '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}'
                  .trim());
          final amount = transaction.amount ?? '';

          bytes += generator.text('$paymentMethodName: $amount SR');
        }
      }
    } catch (e) {
      Get.log('Error in payment methods generation: $e');
      // Skip payment methods if there's an error
    }

    bytes += generator.emptyLines(2);

    try {
      // Footer - simplified
      if (websiteUrl.isNotEmpty) {
        bytes += generator.text('Powered by $websiteUrl',
            styles: const PosStyles(align: PosAlign.center));
      }
      bytes += generator.emptyLines(2);

      // XPrinter K200L specific ending commands
      bytes += generator.feed(2);
      bytes += generator.cut();

      Get.log('Generated ${bytes.length} bytes for XPrinter K200L');
      return bytes;
    } catch (e) {
      Get.log('Error in footer generation: $e');
      // Minimal ending
      bytes += generator.emptyLines(3);
      bytes += generator.cut();
      return bytes;
    }
  }

  /// Generate PDF invoice for web printing
  Future<Uint8List> _generatePdfInvoice(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    final sanitizedRestaurantName = _sanitizeText(restaurntName);
    final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
    final paymentTransactions =
        orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
            [];

    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // Header
              pw.Text(
                sanitizedRestaurantName,
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
                textAlign: pw.TextAlign.center,
              ),
              pw.Text(
                'Tax Invoice',
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
                textAlign: pw.TextAlign.center,
              ),
              pw.SizedBox(height: 20),

              // Order number
              pw.Text(
                'Order #$invoiceNumber',
                style:
                    pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
                textAlign: pw.TextAlign.center,
              ),
              pw.SizedBox(height: 20),

              // Company info
              pw.Text('VAT: $vatNumber'),
              pw.Text('C.R: $commerce'),
              pw.Text('Address: ${_sanitizeText(mainAddress)}'),
              pw.Text('Cashier: ${_sanitizeText(cashierName)}'),
              pw.Text(
                  'Date: ${DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now())}'),

              pw.SizedBox(height: 20),
              pw.Divider(),

              // Items
              pw.Text(
                'ITEMS',
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Divider(),

              ...items.map((item) {
                final itemName = _sanitizeText(item.productName ?? 'Item');
                final qty = item.qty ?? '0';
                final price = item.totalPrice ?? '0.00';
                return pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('$qty x $itemName'),
                    pw.Text('$price SR'),
                  ],
                );
              }),

              pw.Divider(),

              // Totals
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('Subtotal:',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                  pw.Text('${subtotal.toStringAsFixed(2)} SR',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ],
              ),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('VAT ($vat%):',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                  pw.Text('${vatAmount.toStringAsFixed(2)} SR',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ],
              ),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('TOTAL:',
                      style: pw.TextStyle(
                          fontSize: 16, fontWeight: pw.FontWeight.bold)),
                  pw.Text('${totalIncludingVat.toStringAsFixed(2)} SR',
                      style: pw.TextStyle(
                          fontSize: 16, fontWeight: pw.FontWeight.bold)),
                ],
              ),

              pw.Divider(),

              // Payment methods
              if (paymentTransactions.isNotEmpty) ...[
                pw.Text('PAYMENT METHODS:',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ...paymentTransactions.map((transaction) {
                  final paymentMethodName = _sanitizeText(
                      '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}'
                          .trim());
                  final amount = transaction.amount ?? '';
                  return pw.Text('$paymentMethodName: $amount SR');
                }),
              ],

              pw.SizedBox(height: 20),

              // Footer
              if (websiteUrl.isNotEmpty) pw.Text('Powered by $websiteUrl'),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Print to web printer using HTTP requests
  Future<bool> _printToWebPrinter(
    String printerIP,
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    try {
      Get.log('Attempting web print to $printerIP');

      // Generate ESC/POS commands (same as mobile)
      final profile = await CapabilityProfile.load();
      final List<int> bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      // Try multiple methods to send data to printer
      final List<int> portsToTry = [9100, 515, 631, 8080, 80];

      for (int port in portsToTry) {
        try {
          Get.log('Trying web print to $printerIP:$port');

          // Method 1: Try HTTP POST to printer
          final success = await _sendHttpToPrinter(printerIP, port, bytes);
          if (success) {
            Get.log('Successfully printed via HTTP to $printerIP:$port');
            return true;
          }
        } catch (e) {
          Get.log('HTTP print failed for $printerIP:$port - $e');
          continue;
        }
      }

      return false;
    } catch (e) {
      Get.log('Web printer error for $printerIP: $e');
      return false;
    }
  }

  /// Send ESC/POS data to printer via HTTP
  Future<bool> _sendHttpToPrinter(
      String printerIP, int port, List<int> bytes) async {
    try {
      // Try sending raw ESC/POS data via HTTP POST
      final uri = Uri.parse('http://$printerIP:$port/');

      final response = await http
          .post(
            uri,
            headers: {
              'Content-Type': 'application/octet-stream',
              'Content-Length': bytes.length.toString(),
            },
            body: bytes,
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      }

      Get.log('HTTP response: ${response.statusCode} - ${response.body}');
      return false;
    } catch (e) {
      Get.log('HTTP send error: $e');
      return false;
    }
  }

  /// Fallback PDF printing for web
  Future<void> _printInvoiceWebPdf(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
  ) async {
    try {
      Get.log('Using PDF fallback for web printing');

      // Generate PDF invoice
      final pdfBytes = await _generatePdfInvoice(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
      );

      // Open print dialog using the printing package
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_$invoiceNumber',
        format: PdfPageFormat.a4,
      );

      Get.snackbar(
        'Info'.tr,
        'Direct printer connection failed. Using PDF print dialog.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.secondary,
        colorText: Get.theme.colorScheme.onSecondary,
      );
    } catch (e) {
      Get.log('PDF fallback error: $e');
      rethrow;
    }
  }
}
