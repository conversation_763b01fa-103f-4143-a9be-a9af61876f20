import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  /// Helper method to handle Arabic text encoding

  final OrderController orderController = Get.find<OrderController>();

  /// Debug method to test invoice generation without printing
  Future<void> testInvoiceGeneration(PrintController controller) async {
    try {
      Get.log('Testing invoice generation...');

      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = double.parse(controller.totalPrice.toString());
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      final profile = await CapabilityProfile.load();

      final bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      Get.log('Invoice generation successful! Generated ${bytes.length} bytes');
      successSnackBar(
        'Invoice generation test successful! ${bytes.length} bytes generated',
      );
    } catch (e) {
      Get.log('Invoice generation failed: $e');
      failedSnaskBar(
        'Invoice generation test failed: $e',
      );
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // Check if running on web
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }

    // Mobile/Desktop printing logic
    await _printInvoiceMobile(controller);
  }

  /// Web-specific printing method - uses image capture of the invoice widget
  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');

      // Capture the invoice widget as an image and convert to PDF
      await _printInvoiceAsImagePdf(controller);

      // Show success message
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  /// Print invoice by capturing the widget as an image and converting to PDF
  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image');

      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image
      final imageBytes = await _captureWidgetAsImage(invoiceWidget);

      // Convert image to PDF
      final pdfBytes = await _createPdfFromImage(imageBytes);

      // Open print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(Widget widget) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      // Create a properly structured widget for capture
      final captureWidget = MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SingleChildScrollView(
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: widget,
            ),
          ),
        ),
        debugShowCheckedModeBanner: false,
      );

      // Use screenshot controller to capture the widget
      final imageBytes = await screenshotController.captureFromWidget(
        captureWidget,
        pixelRatio: 2.0,
        context: Get.context,
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  /// Create a PDF from an image
  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();

    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Print ESC/POS data to a single printer
  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');

      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);

      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  /// Mobile/Desktop printing method - now uses image capture
  Future<void> _printInvoiceMobile(PrintController controller) async {
    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting ESC/POS print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Get cached invoice data
      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Calculate totals
      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = controller.totalPrice;
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      // Generate ESC/POS commands using existing method
      final profile = await CapabilityProfile.load();
      final escPosBytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Generate ESC/POS commands for the invoice
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Create generator for ESC/POS commands optimized for XPrinter K200L
    final generator = Generator(PaperSize.mm80, profile);
    final List<int> bytes = <int>[];

    try {
      // Initialize printer
      bytes.addAll(List<int>.from(generator.reset()));

      // Restaurant Name (matches InvoiceTitle widget)
      bytes.addAll(List<int>.from(generator.text(
        restaurntName,
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
        ),
      )));

      // Arabic title: "فاتورة ضريبية مبسطة" (matches print_view)
      bytes.addAll(List<int>.from(generator.text(
        'فاتورة ضريبية مبسطة',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
        ),
      )));

      // English title: "Simplified Tax Invoice" (matches print_view)
      bytes.addAll(List<int>.from(generator.text(
        'Simplified Tax Invoice',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
        ),
      )));

      bytes.addAll(List<int>.from(generator.emptyLines(1)));
    } catch (e) {
      Get.log('Error in header generation: $e');
      // Fallback to basic text
      bytes.addAll(List<int>.from(generator.text('INVOICE',
          styles: const PosStyles(align: PosAlign.center, bold: true))));
      bytes.addAll(List<int>.from(generator.emptyLines(1)));
    }

    try {
      // Order Number (matches InvoiceNumberBox widget)
      bytes.addAll(List<int>.from(generator.text(
        'Order #${invoiceNumber.trim()}',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
        ),
      )));
      bytes.addAll(List<int>.from(generator.emptyLines(1)));
    } catch (e) {
      Get.log('Error in order number generation: $e');
      bytes.addAll(List<int>.from(generator.text('Order: $invoiceNumber')));
      bytes.addAll(List<int>.from(generator.emptyLines(1)));
    }

    try {
      // Company Information Table (matches CompanyInfoTable widget)
      // VAT - الرقم الضريبي
      bytes.addAll(List<int>.from(generator.text(
        'VAT: $vatNumber - الرقم الضريبي',
        styles: const PosStyles(align: PosAlign.center),
      )));

      // C.R - السجل التجاري
      bytes.addAll(List<int>.from(generator.text(
        'C.R: $commerce - السجل التجاري',
        styles: const PosStyles(align: PosAlign.center),
      )));

      // POS - نقطة البيع
      bytes.addAll(List<int>.from(generator.text(
        'POS: $mainAddress - نقطة البيع',
        styles: const PosStyles(align: PosAlign.center),
      )));

      // Cashier - الكاشير
      bytes.addAll(List<int>.from(generator.text(
        'Cashier: $cashierName - الكاشير',
        styles: const PosStyles(align: PosAlign.center),
      )));

      // Date - التاريخ
      bytes.addAll(List<int>.from(generator.text(
        'Date: ${DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now())} - التاريخ',
        styles: const PosStyles(align: PosAlign.center),
      )));
    } catch (e) {
      Get.log('Error in company info generation: $e');
      // Fallback to basic info
      bytes.addAll(List<int>.from(generator.text('VAT: $vatNumber')));
      bytes.addAll(List<int>.from(generator
          .text('Date: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}')));
    }

    bytes.addAll(List<int>.from(generator.hr()));

    try {
      // Column Headers (matches InvoiceColumnHeaders widget)
      bytes.addAll(List<int>.from(generator.text(
        'Qty - الكمية    Item - الصنف    Price - السعر',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));
      bytes.addAll(List<int>.from(generator.hr()));

      // Print items (matches InvoiceItemRow widget)
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        final itemName = item.productName ?? 'Item';
        final qty = item.qty ?? '0';
        final price = '${item.totalPrice ?? '0.00'} SR';

        // Format: Qty  Item  Price (matches the row layout)
        final itemLine = '$qty    $itemName    $price';
        bytes.addAll(List<int>.from(generator.text(itemLine)));
      }
    } catch (e) {
      Get.log('Error in items generation: $e');
      // Fallback to basic items list
      bytes.addAll(List<int>.from(generator.text('ITEMS:')));
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        bytes.addAll(List<int>.from(generator
            .text('${item.qty ?? '0'} x ${item.productName ?? 'Item'}')));
      }
    }

    bytes.addAll(List<int>.from(generator.hr()));

    try {
      // Totals (matches InvoiceTotalRow widgets with Arabic text)
      bytes.addAll(List<int>.from(generator.text(
        'المجموع بدون الضريبة - Subtotal: ${subtotal.toStringAsFixed(2)} SR',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.text(
        '%($vat) الضريبة - VAT: ${vatAmount.toStringAsFixed(2)} SR',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.text(
        'الإجمالي شامل الضريبة - Total including VAT: ${totalIncludingVat.toStringAsFixed(2)} SR',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
        ),
      )));
    } catch (e) {
      Get.log('Error in totals generation: $e');
      // Fallback to basic totals
      bytes.addAll(List<int>.from(
          generator.text('Subtotal: ${subtotal.toStringAsFixed(2)} SR')));
      bytes.addAll(List<int>.from(
          generator.text('VAT: ${vatAmount.toStringAsFixed(2)} SR')));
      bytes.addAll(List<int>.from(generator.text(
          'TOTAL: ${totalIncludingVat.toStringAsFixed(2)} SR',
          styles: const PosStyles(bold: true))));
    }

    bytes.addAll(List<int>.from(generator.hr()));

    try {
      // Payment methods - simplified
      final paymentTransactions =
          orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
              [];

      if (paymentTransactions.isNotEmpty) {
        bytes.addAll(List<int>.from(generator.text('PAYMENT METHODS:',
            styles: const PosStyles(bold: true))));

        for (final transaction in paymentTransactions) {
          final paymentMethodName =
              '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}'
                  .trim();
          final amount = transaction.amount ?? '';

          bytes.addAll(
              List<int>.from(generator.text('$paymentMethodName: $amount SR')));
        }
      }
    } catch (e) {
      Get.log('Error in payment methods generation: $e');
      // Skip payment methods if there's an error
    }

    bytes.addAll(List<int>.from(generator.emptyLines(2)));

    try {
      // Footer - simplified
      if (websiteUrl.isNotEmpty) {
        bytes.addAll(List<int>.from(generator.text('Powered by $websiteUrl',
            styles: const PosStyles(align: PosAlign.center))));
      }
      bytes.addAll(List<int>.from(generator.emptyLines(2)));

      // XPrinter K200L specific ending commands
      bytes.addAll(List<int>.from(generator.feed(2)));
      bytes.addAll(List<int>.from(generator.cut()));

      Get.log('Generated ${bytes.length} bytes for XPrinter K200L');
      return bytes;
    } catch (e) {
      Get.log('Error in footer generation: $e');
      // Minimal ending
      bytes.addAll(List<int>.from(generator.emptyLines(3)));
      bytes.addAll(List<int>.from(generator.cut()));
      return bytes;
    }
  }
}
