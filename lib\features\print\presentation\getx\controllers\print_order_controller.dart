import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  /// Helper method to handle Arabic text encoding

  final OrderController orderController = Get.find<OrderController>();

  /// Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  /// Debug method to test invoice generation without printing
  Future<void> testInvoiceGeneration(PrintController controller) async {
    try {
      Get.log('Testing invoice generation...');

      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = double.parse(controller.totalPrice.toString());
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      final profile = await CapabilityProfile.load();

      final bytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      Get.log('Invoice generation successful! Generated ${bytes.length} bytes');
      successSnackBar(
        'Invoice generation test successful! ${bytes.length} bytes generated',
      );
    } catch (e) {
      Get.log('Invoice generation failed: $e');
      failedSnaskBar(
        'Invoice generation test failed: $e',
      );
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // Check if running on web
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }

    // Mobile/Desktop printing logic
    await _printInvoiceMobile(controller);
  }

  /// Web-specific printing method - uses image capture of the invoice widget
  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');

      // Capture the invoice widget as an image and convert to PDF
      await _printInvoiceAsImagePdf(controller);

      // Show success message
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  /// Print invoice by capturing the widget as an image and converting to PDF
  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image');

      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image
      final imageBytes = await _captureWidgetAsImage(invoiceWidget);

      // Convert image to PDF
      final pdfBytes = await _createPdfFromImage(imageBytes);

      // Open print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(Widget widget) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      // Create a properly structured widget for capture
      final captureWidget = MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SingleChildScrollView(
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: widget,
            ),
          ),
        ),
        debugShowCheckedModeBanner: false,
      );

      // Use screenshot controller to capture the widget
      final imageBytes = await screenshotController.captureFromWidget(
        captureWidget,
        pixelRatio: 2.0,
        context: Get.context,
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  /// Create a PDF from an image
  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();

    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Print ESC/POS data to a single printer
  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');

      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);

      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  /// Mobile/Desktop printing method - now uses image capture
  Future<void> _printInvoiceMobile(PrintController controller) async {
    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting ESC/POS print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Get cached invoice data
      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Calculate totals
      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = controller.totalPrice;
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      // Generate ESC/POS commands using existing method
      final profile = await CapabilityProfile.load();
      final escPosBytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Generate a simple QR code data (you might want to use a proper QR code library)
  String _generateQRData(Map<String, dynamic> invoiceData, String invoiceNumber,
      double totalIncludingVat) {
    final vatNumber = invoiceData['vatNumber'] as String;
    final restaurantName = invoiceData['restaurantName'] as String;
    final date = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

    // Simple QR data format - you can customize this based on your requirements
    return 'Company: $restaurantName\nVAT: $vatNumber\nInvoice: $invoiceNumber\nTotal: ${totalIncludingVat.toStringAsFixed(2)} SR\nDate: $date';
  }

  /// Generate ESC/POS commands for the invoice matching the image design
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    final restaurantName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final commerce = invoiceData['commerce'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final vat = invoiceData['vat'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    // Create generator for ESC/POS commands
    final generator = Generator(PaperSize.mm80, profile);
    final List<int> bytes = <int>[];

    try {
      // Initialize printer
      bytes.addAll(List<int>.from(generator.reset()));

      // Logo section
      final logoUrl = invoiceData['logo'] as String? ?? '';
      if (logoUrl.isNotEmpty) {
        try {
          final logoBytes = await _fetchLogoBytes(logoUrl);
          final logoImage = img.decodeImage(logoBytes);
          if (logoImage != null) {
            final resizedLogo = img.copyResize(logoImage, width: 200);
            bytes.addAll(List<int>.from(
                generator.imageRaster(resizedLogo, align: PosAlign.center)));
            bytes.addAll(List<int>.from(generator.emptyLines(1)));
          }
        } catch (e) {
          Get.log('Error printing logo: $e');
        }
      }

      // Company name - centered and bold
      bytes.addAll(List<int>.from(generator.text(
        restaurantName,
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
        ),
      )));

      bytes.addAll(List<int>.from(generator.emptyLines(1)));

      // Arabic subtitle
      bytes.addAll(List<int>.from(generator.text(
        'شركة أجيال لبيئة المعلومات',
        styles: const PosStyles(align: PosAlign.center),
      )));

      // English subtitle
      bytes.addAll(List<int>.from(generator.text(
        'Ajyal for Information Environment Company',
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.emptyLines(1)));

      // Invoice type headers
      bytes.addAll(List<int>.from(generator.text(
        'فاتورة ضريبية مبسطة',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.text(
        'Simplified Tax Invoice',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.emptyLines(1)));

      // Order number - large and centered
      bytes.addAll(List<int>.from(generator.text(
        'Order #$invoiceNumber',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
          width: PosTextSize.size2,
        ),
      )));

      bytes.addAll(List<int>.from(generator.emptyLines(1)));
      bytes.addAll(List<int>.from(generator.hr()));

      // Company information in table format
      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'الرقم الضريبي',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'VAT',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        vatNumber,
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'السجل التجاري',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'C.R',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        commerce,
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'نقطة البيع',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'POS',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        mainAddress,
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'الكاشير',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'Cashier',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        cashierName,
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'التاريخ',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'Date',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now()),
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.hr()));

      // Table headers
      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'الكمية',
            width: 2,
            styles: const PosStyles(align: PosAlign.center, bold: true)),
        PosColumn(
            text: 'Qty',
            width: 2,
            styles: const PosStyles(align: PosAlign.center, bold: true)),
        PosColumn(
            text: 'الصنف',
            width: 4,
            styles: const PosStyles(align: PosAlign.center, bold: true)),
        PosColumn(
            text: 'Item',
            width: 2,
            styles: const PosStyles(align: PosAlign.center, bold: true)),
        PosColumn(
            text: 'السعر',
            width: 2,
            styles: const PosStyles(align: PosAlign.center, bold: true)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        'Price',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      bytes.addAll(List<int>.from(generator.hr()));

      // Print items
      final items = orderDetailsController.orderDetailsModel.data?.items ?? [];
      for (final item in items) {
        final itemName = item.productName ?? 'Item';
        final qty = item.qty ?? '0';
        final price = '${item.totalPrice ?? '0.00'} SR';

        bytes.addAll(List<int>.from(generator.text(
          qty,
          styles: const PosStyles(align: PosAlign.center),
        )));

        bytes.addAll(List<int>.from(generator.text(
          itemName,
          styles: const PosStyles(align: PosAlign.center),
        )));

        bytes.addAll(List<int>.from(generator.text(
          price,
          styles: const PosStyles(align: PosAlign.center),
        )));

        bytes.addAll(List<int>.from(generator.emptyLines(1)));
      }

      bytes.addAll(List<int>.from(generator.hr()));

      // Totals section
      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'المجموع بدون الضريبة',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'Subtotal',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        '${subtotal.toStringAsFixed(2)} SR',
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'الضريبة %$vat',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'VAT %$vat',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        '${vatAmount.toStringAsFixed(2)} SR',
        styles: const PosStyles(align: PosAlign.center),
      )));

      bytes.addAll(List<int>.from(generator.row([
        PosColumn(
            text: 'الإجمالي شامل الضريبة',
            width: 6,
            styles: const PosStyles(align: PosAlign.right)),
        PosColumn(
            text: 'Total including VAT',
            width: 6,
            styles: const PosStyles(align: PosAlign.left)),
      ])));

      bytes.addAll(List<int>.from(generator.text(
        '${totalIncludingVat.toStringAsFixed(2)} SR',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
        ),
      )));

      bytes.addAll(List<int>.from(generator.hr()));

      // Payment methods
      final paymentTransactions =
          orderDetailsController.orderDetailsModel.data?.paymentTransactions ??
              [];
      if (paymentTransactions.isNotEmpty) {
        for (final transaction in paymentTransactions) {
          final paymentMethodName =
              '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}'
                  .trim();
          final amount = transaction.amount ?? '';

          bytes.addAll(List<int>.from(generator.text(
            '$paymentMethodName payment',
            styles: const PosStyles(align: PosAlign.center),
          )));

          bytes.addAll(List<int>.from(generator.text(
            '$amount SR',
            styles: const PosStyles(align: PosAlign.center, bold: true),
          )));
        }
        bytes.addAll(List<int>.from(generator.emptyLines(1)));
      }

      // QR Code section
      bytes.addAll(List<int>.from(generator.emptyLines(1)));

      // Generate QR code data
      final qrData =
          _generateQRData(invoiceData, invoiceNumber, totalIncludingVat);

      // Print QR code placeholder (you'll need to implement actual QR code generation)
      bytes.addAll(List<int>.from(generator.text(
        '[QR CODE]',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      )));

      // You can use a QR code library here to generate the actual QR code
      // For example: qr_flutter package to generate QR code image
      // Then convert it to ESC/POS format and add it here

      bytes.addAll(List<int>.from(generator.emptyLines(2)));

      // Footer
      if (websiteUrl.isNotEmpty) {
        bytes.addAll(List<int>.from(generator.text(
          'Powered by $websiteUrl',
          styles: const PosStyles(align: PosAlign.center),
        )));
      }

      bytes.addAll(List<int>.from(generator.emptyLines(3)));
      bytes.addAll(List<int>.from(generator.cut()));

      Get.log('Generated ${bytes.length} bytes for thermal printer');
      return bytes;
    } catch (e) {
      Get.log('Error in ESC/POS generation: $e');
      // Minimal fallback
      bytes.addAll(List<int>.from(generator.text('INVOICE ERROR')));
      bytes.addAll(List<int>.from(generator.cut()));
      return bytes;
    }
  }
}
